import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Tag, Building2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useCoupons } from "@/hooks/useCoupons";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils";
import { useUpdateCouponStatus } from "@/hooks/useUpdateCouponStatus";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Coupon } from "@/types/api";

// Component to display company information for company-owned coupons
const CompanyInfo: React.FC<{ coupon: Coupon }> = ({ coupon }) => {
  // Only render for company-owned coupons
  if (coupon.owner_type !== "company") {
    return null;
  }

  // Check if company information is available
  if (!coupon.company_name || !coupon.company_cnpj) {
    return null;
  }

  return (
    <Badge variant="secondary" className="text-xs">
      <Building2 className="h-3 w-3 mr-1" />
      {coupon.company_name} - {coupon.company_cnpj}
    </Badge>
  );
};

const Coupons = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [page, setPage] = useState(1);
  const [showInactive, setShowInactive] = useState(false);
  const limit = 10;
  const { data, isLoading, error } = useCoupons(page, limit);
  const updateStatusMutation = useUpdateCouponStatus();

  const handleStatusChange = async (couponId: string, newStatus: boolean) => {
    try {
      await updateStatusMutation.mutateAsync({ couponId, status: newStatus });
      toast({
        title: "Sucesso",
        description: "Status do cupom atualizado com sucesso!",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao atualizar status do cupom",
        variant: "destructive",
      });
    }
  };

  const filteredCoupons = data?.data.filter(coupon => showInactive || coupon.is_active) || [];
  const totalPages = Math.ceil((filteredCoupons.length || 0) / limit);

  if (error) return (
    <div className="flex items-center justify-center h-[50vh]">
      <Card className="w-full max-w-md">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <p className="text-lg font-medium text-destructive">Erro ao carregar cupons</p>
            <Button onClick={() => window.location.reload()}>Tentar novamente</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold">Cupons</h1>
          <p className="text-sm text-muted-foreground">
            Gerencie os cupons de desconto disponíveis
          </p>
        </div>
        <Button onClick={() => navigate("/admin/coupons/new")}>
          <Plus className="mr-2 h-4 w-4" />
          Novo Cupom
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="show-inactive"
          checked={showInactive}
          onCheckedChange={setShowInactive}
        />
        <Label htmlFor="show-inactive">Mostrar cupons inativos</Label>
      </div>

      <Card>
        <CardContent className="p-6">
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="p-4">
                  <div className="space-y-3">
                    <Skeleton className="h-4 w-[250px]" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[200px]" />
                      <Skeleton className="h-4 w-[150px]" />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : filteredCoupons.length === 0 ? (
            <div className="text-center py-8">
              <Tag className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">Nenhum cupom encontrado</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                {showInactive 
                  ? "Não há cupons cadastrados no sistema."
                  : "Não há cupons ativos no momento."}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredCoupons.map((coupon) => (
                <Card key={coupon.external_id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg font-mono">{coupon.code}</CardTitle>
                        <div className="flex flex-wrap gap-2 mt-2">
                          <Badge variant={coupon.is_active ? "default" : "secondary"}>
                            {coupon.is_active ? "Ativo" : "Inativo"}
                          </Badge>
                          <Badge variant="outline">
                            {coupon.type === "percentage" ? "Porcentagem" : "Valor Fixo"}
                          </Badge>
                          {/* Company Information for company-owned coupons */}
                          <CompanyInfo coupon={coupon} />
                        </div>
                      </div>
                      <Button
                        variant={coupon.is_active ? "destructive" : "default"}
                        size="sm"
                        onClick={() => handleStatusChange(coupon.external_id, !coupon.is_active)}
                      >
                        {coupon.is_active ? "Desativar" : "Ativar"}
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Valor do Desconto</p>
                        <p className="font-medium">
                          {coupon.type === "percentage" 
                            ? `${coupon.value / 100}%` 
                            : formatCurrency(coupon.value / 100)}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Valor Mínimo</p>
                        <p className="font-medium">{formatCurrency(coupon.min_order_value / 100)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Uso</p>
                        <p className="font-medium">{coupon.used_count}/{coupon.original_quantity}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Expira em</p>
                        <p className="font-medium">
                          {new Date(coupon.expires_at).toLocaleDateString('pt-BR', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric'
                          })}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {!isLoading && totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  onClick={() => setPage(p => Math.max(1, p - 1))}
                  className={page === 1 ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => (
                <PaginationItem key={pageNum}>
                  <PaginationLink
                    onClick={() => setPage(pageNum)}
                    isActive={page === pageNum}
                  >
                    {pageNum}
                  </PaginationLink>
                </PaginationItem>
              ))}
              <PaginationItem>
                <PaginationNext
                  onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                  className={page === totalPages ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
};

export default Coupons; 